package cn.byssted.bbs.bbsrd.controller;

import cn.byssted.bbs.bbsrd.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = "*")
public class TestController {

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("message", "BBS论坛系统运行正常");
        return Result.success("系统健康", data);
    }

    /**
     * 版本信息
     */
    @GetMapping("/version")
    public Result<Map<String, String>> version() {
        Map<String, String> data = new HashMap<>();
        data.put("version", "1.0.0");
        data.put("name", "BBS论坛系统");
        data.put("description", "基于SpringBoot3 + MySQL + Vue3的BBS论坛系统");
        return Result.success("版本信息", data);
    }
}
