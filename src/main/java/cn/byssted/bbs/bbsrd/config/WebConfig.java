package cn.byssted.bbs.bbsrd.config;

import cn.byssted.bbs.bbsrd.interceptor.JwtAuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private JwtAuthInterceptor jwtAuthInterceptor;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*") // 确保使用 allowedOriginPatterns 而不是 allowedOrigins
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtAuthInterceptor)
                .addPathPatterns("/api/**") // 拦截所有API请求
                .excludePathPatterns(
                        "/api/users/login",     // 排除登录接口
                        "/api/users/register",  // 排除注册接口
                        "/api/test/**",         // 排除测试接口
                        "/api/posts",           // 排除获取帖子列表（游客可访问）
                        "/api/posts/{id}",      // 排除获取帖子详情（游客可访问）
                        "/api/comments/post/**", // 排除获取评论列表（游客可访问）
                        "/api/sections",        // 排除获取板块列表（游客可访问）
                        "/api/sections/{id}"    // 排除获取板块详情（游客可访问）
                );
    }
}
