# JWT认证拦截器重构说明

## 重构概述

本次重构将原本分散在各个Controller中的JWT认证逻辑统一提取到拦截器中实现，大大简化了代码结构，提高了可维护性。

## 重构内容

### 1. 新增文件

#### 1.1 自定义注解
- `src/main/java/cn/byssted/bbs/bbsrd/annotation/AuthRequired.java` - 标记需要登录认证的接口
- `src/main/java/cn/byssted/bbs/bbsrd/annotation/AdminRequired.java` - 标记需要管理员权限的接口

#### 1.2 拦截器
- `src/main/java/cn/byssted/bbs/bbsrd/interceptor/JwtAuthInterceptor.java` - JWT认证拦截器

### 2. 修改文件

#### 2.1 配置类
- `src/main/java/cn/byssted/bbs/bbsrd/config/WebConfig.java` - 添加拦截器配置

#### 2.2 Controller重构
- `UserController.java` - 移除重复认证逻辑，添加@AuthRequired注解
- `PostController.java` - 移除重复认证逻辑，添加@AuthRequired注解
- `CommentController.java` - 移除重复认证逻辑，添加@AuthRequired注解
- `AdminController.java` - 移除重复认证逻辑，添加@AdminRequired注解
- `SectionController.java` - 移除重复认证逻辑，添加@AdminRequired注解

## 重构优势

### 1. 代码简化
- **重构前**: 每个需要认证的接口都要重复编写token验证逻辑（约15-20行代码）
- **重构后**: 只需添加一个注解即可

### 2. 统一管理
- 所有认证逻辑集中在拦截器中，便于维护和修改
- 统一的错误处理和日志记录

### 3. 灵活配置
- 通过WebConfig可以灵活配置哪些路径需要拦截
- 支持方法级别和类级别的注解

### 4. 性能优化
- 减少了重复代码的执行
- 统一的token解析和验证

## 使用方式

### 1. 普通用户认证
```java
@AuthRequired
@GetMapping("/profile")
public Result<User> getProfile(HttpServletRequest request) {
    Long userId = (Long) request.getAttribute("userId");
    // 业务逻辑...
}
```

### 2. 管理员权限认证
```java
@AdminRequired
@PostMapping
public Result<Section> createSection(@RequestBody Map<String, String> request) {
    // 业务逻辑...
}
```

### 3. 整个Controller需要管理员权限
```java
@RestController
@RequestMapping("/api/admin")
@AdminRequired
public class AdminController {
    // 所有方法都需要管理员权限
}
```

## 拦截器配置

在WebConfig中配置了以下排除路径（游客可访问）：
- `/api/users/login` - 登录接口
- `/api/users/register` - 注册接口
- `/api/test/**` - 测试接口
- `/api/posts` - 获取帖子列表
- `/api/posts/{id}` - 获取帖子详情
- `/api/comments/post/**` - 获取评论列表
- `/api/sections` - 获取板块列表
- `/api/sections/{id}` - 获取板块详情

## 注意事项

1. **请求属性获取**: 在Controller中通过`request.getAttribute("userId")`获取用户ID
2. **错误处理**: 拦截器统一返回JSON格式的错误响应
3. **日志记录**: 拦截器会记录认证失败的详细日志
4. **Token格式**: 仍然使用`Authorization: Bearer <token>`格式

## 测试建议

1. 测试未登录用户访问需要认证的接口
2. 测试普通用户访问管理员接口
3. 测试Token过期的情况
4. 测试游客可访问的接口

## 总结

通过这次重构，我们成功地：
- 消除了大量重复代码（每个需要认证的方法减少了约15-20行代码）
- 提高了代码的可维护性和可读性
- 实现了统一的认证和权限管理
- 保持了原有的功能不变

这是一个典型的面向切面编程(AOP)的应用，通过拦截器实现了横切关注点的统一处理。
